        /* Styles généraux */
        body {
            font-family: '<PERSON><PERSON>', 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.6;
            background-color: #f8f9fa;
            direction: ltr;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .btn {
            display: inline-block;
            background-color: #d4af37;
            color: white;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 700;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background-color: #a68b2a;
        }
        
        .whatsapp-btn, .btn.whatsapp-btn {
            background-color: #25D366;
            color: white;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            border: none;
        }

        .whatsapp-btn i {
            margin-right: 8px;
        }
        
        .whatsapp-btn:hover {
            background-color: #128C7E;
        }

        /* Barre de navigation */
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 5%;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .logo h1 {
            margin: 0;
            color: #d4af37;
            font-weight: 700;
            font-size: 1.8rem;
        }
        .logo p {
            margin: 0;
            font-size: 0.8rem;
            color: #6c757d;
        }

        .nav-links a {
            margin: 0 15px;
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #d4af37;
        }

        /* Sélecteur de langue */
        .language-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .language-selector a {
            padding: 5px 10px;
            border-radius: 3px;
            text-decoration: none;
            color: #333;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .language-selector a:hover {
            background-color: #f0f0f0;
        }

        .language-selector a.active {
            background-color: #d4af37;
            color: white;
        }

        /* Section héro */
        .hero {
            display: flex;
            align-items: center;
            padding: 5rem 5%;
            background: linear-gradient(135deg, #f9f9f9 0%, #ffffff 100%);
        }

        .hero-content {
            flex: 1;
            padding-right: 3rem;
        }

        .hero-content h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #343a40;
            line-height: 1.2;
        }
        
        .hero-content p {
            font-size: 1.1rem;
            color: #555;
        }

        .hero-image {
            flex: 1;
            text-align: center;
        }

        .hero-image img {
            max-width: 100%;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .hero-buttons {
            margin-top: 2rem;
            display: flex;
            gap: 15px;
        }

        /* Produits */
        .section-title {
            text-align: center;
            margin-bottom: 3rem;
            font-size: 2.5rem;
            color: #d4af37;
            font-weight: 700;
        }
        
        .products {
            padding: 5rem 0;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .product-card {
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            text-align: center;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 25px rgba(0,0,0,0.15);
        }

        .product-card img {
            width: 100%;
            height: 300px;
            object-fit: cover;
        }

        .product-card-body {
            padding: 1.5rem;
        }

        .product-card h3 {
            margin: 0 0 0.5rem;
            color: #343a40;
        }
        
        .product-card p {
            color: #6c757d;
        }

        .product-card .price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #d4af37;
            margin: 1rem 0;
        }
        
        /* Caractéristiques */
        .features {
            padding: 5rem 0;
            background-color: #fff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .feature {
            text-align: center;
            padding: 2.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s;
        }
        
        .feature:hover {
            transform: scale(1.05);
        }

        .feature i {
            font-size: 3rem;
            color: #d4af37;
            margin-bottom: 1rem;
        }
        
        .feature h3 {
            font-size: 1.5rem;
        }

        /* Témoignages */
        .testimonials {
            padding: 5rem 0;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .testimonial {
            background: #fff;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            text-align: center;
        }

        .testimonial img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 1rem;
            object-fit: cover;
        }

        .testimonial h4 {
            margin: 0.5rem 0;
            color: #d4af37;
        }

        .testimonial .stars {
            color: #ffc107;
            margin-bottom: 1rem;
        }

        /* Contact */
        .contact {
            padding: 5rem 0;
            background-color: #fff;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .contact-method {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .contact-method i {
            font-size: 3rem;
            color: #d4af37;
            margin-bottom: 1rem;
        }

        .contact-method h3 {
            margin: 1rem 0;
            color: #343a40;
        }

        .contact-method p {
            color: #6c757d;
            margin-bottom: 1.5rem;
        }

        /* Pied de page */
        footer {
            background-color: #343a40;
            color: #fff;
            padding: 3rem 0 0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .footer-section h3 {
            color: #d4af37;
            margin-bottom: 1rem;
        }

        .footer-section ul {
            list-style: none;
            padding: 0;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-section ul li a:hover {
            color: #d4af37;
        }

        .footer-bottom {
            background-color: #212529;
            padding: 1rem 0;
            margin-top: 2rem;
            text-align: center;
        }

        .footer-bottom p {
            margin: 0;
            color: #ccc;
        }

        /* WhatsApp flottant */
        .floating-whatsapp {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #25D366;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.3s;
            z-index: 1000;
        }

        .floating-whatsapp:hover {
            transform: scale(1.1);
        }

        /* Section de filtrage */
        .filter-section {
            background-color: #f8f9fa;
            padding: 2rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
            justify-content: center;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-group label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .filter-group select,
        .filter-group input {
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .filter-btn, .reset-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .filter-btn {
            background-color: #d4af37;
            color: white;
        }

        .filter-btn:hover {
            background-color: #a68b2a;
        }

        .reset-btn {
            background-color: #6c757d;
            color: white;
        }

        .reset-btn:hover {
            background-color: #545b62;
        }

        /* Responsive */
        @media (max-width: 768px) {
            nav {
                flex-direction: column;
                padding: 1rem;
            }
            
            .nav-links {
                margin: 1rem 0;
                text-align: center;
            }
            
            .hero {
                flex-direction: column;
                text-align: center;
                padding: 3rem 5%;
            }
            
            .hero-content {
                padding-right: 0;
                margin-bottom: 2rem;
            }
            
            .hero-content h2 {
                font-size: 2rem;
            }
            
            .hero-buttons {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .filter-container {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
            }

            .filter-group label {
                margin-bottom: 0;
                min-width: 100px;
            }

            .floating-whatsapp {
                bottom: 15px;
                right: 15px;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .hero-content h2 {
                font-size: 1.5rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
                padding: 0 2%;
            }
            
            .features-grid,
            .testimonials-grid,
            .contact-grid {
                grid-template-columns: 1fr;
                padding: 0 2%;
            }
        }
