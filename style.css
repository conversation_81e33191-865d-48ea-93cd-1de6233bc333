        /* أنماط عامة */
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.6;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .btn {
            display: inline-block;
            background-color: #d4af37;
            color: white;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 700;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background-color: #a68b2a;
        }
        
        .whatsapp-btn, .btn.whatsapp-btn {
            background-color: #25D366;
            color: white;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            border: none;
        }

        .whatsapp-btn i {
            margin-left: 8px;
        }
        
        .whatsapp-btn:hover {
            background-color: #128C7E;
        }

        /* الشريط العلوي */
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 5%;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .logo h1 {
            margin: 0;
            color: #d4af37;
            font-weight: 700;
            font-size: 1.8rem;
        }
        .logo p {
            margin: 0;
            font-size: 0.8rem;
            color: #6c757d;
        }

        .nav-links a {
            margin: 0 15px;
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #d4af37;
        }

        /* قسم الهيرو */
        .hero {
            display: flex;
            flex-direction: row-reverse; /* تعديل لتقديم الصورة على اليمين */
            align-items: center;
            padding: 5rem 5%;
            background: linear-gradient(135deg, #f9f9f9 0%, #ffffff 100%);
        }

        .hero-content {
            flex: 1;
            padding-right: 3rem;
        }

        .hero-content h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #343a40;
            line-height: 1.2;
        }
        
        .hero-content p {
            font-size: 1.1rem;
            color: #555;
        }

        .hero-image {
            flex: 1;
            text-align: center;
        }

        .hero-image img {
            max-width: 100%;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .hero-buttons {
            margin-top: 2rem;
            display: flex;
            gap: 15px;
        }

        /* المنتجات */
        .section-title {
            text-align: center;
            margin-bottom: 3rem;
            font-size: 2.5rem;
            color: #d4af37;
            font-weight: 700;
        }
        
        .products {
            padding: 5rem 0;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .product-card {
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            text-align: center;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 25px rgba(0,0,0,0.15);
        }

        .product-card img {
            width: 100%;
            height: 300px;
            object-fit: cover;
        }

        .product-card-body {
            padding: 1.5rem;
        }

        .product-card h3 {
            margin: 0 0 0.5rem;
            color: #343a40;
        }
        
        .product-card p {
            color: #6c757d;
        }

        .product-card .price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #d4af37;
            margin: 1rem 0;
        }
        
        /* المميزات */
        .features {
            padding: 5rem 0;
            background-color: #fff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .feature {
            text-align: center;
            padding: 2.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s;
        }
        
        .feature:hover {
            transform: scale(1.05);
        }

        .feature i {
            font-size: 3rem;
            color: #d4af37;
            margin-bottom: 1rem;
        }
        
        .feature h3 {
            font-size: 1.5rem;
        }

        /* آراء العملاء */
        .testimonials {
            padding: 5rem 0;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            padding: 0 5%;
        }

        .testimonial {
            background: #fff;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            text-align: center;
        }

        .stars {
            color: #FFD700;
            margin-bottom: 1rem;
        }
        
        .testimonial p {
            font-style: italic;
            color: #555;
        }

        .client {
            font-weight: 700;
            margin-top: 1rem;
            color: #d4af37;
        }

        /* الاتصال */
        .contact {
            background: linear-gradient(135deg, #f9f9f9 0%, #ffffff 100%);
            padding: 5rem 0;
        }

        .contact-content {
            text-align: center;
        }

        .contact-content p {
            font-size: 1.1rem;
            color: #555;
            margin-top: -1rem;
            margin-bottom: 3rem;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .contact-method {
            text-align: center;
            padding: 2.5rem;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .contact-method i {
            font-size: 3rem;
            color: #d4af37;
            margin-bottom: 1rem;
        }
        
        .contact-method h3 {
            font-size: 1.5rem;
        }

        /* الفوتر */
        footer {
            background: #212529;
            color: #fff;
            padding: 3rem 5% 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #d4af37;
            margin-bottom: 1rem;
        }

        .footer-section ul {
            list-style: none;
            padding: 0;
        }

        .footer-section ul li {
            margin-bottom: 0.8rem;
        }

        .footer-section ul li a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-section ul li a:hover {
            color: #d4af37;
        }

        .social-icons a {
            color: #fff;
            margin-right: 15px;
            font-size: 1.5rem;
            transition: color 0.3s;
        }
        
        .social-icons a:hover {
            color: #d4af37;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 1rem;
            border-top: 1px solid #444;
        }

        /* زر الواتساب العائم */
        .floating-whatsapp {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background-color: #25D366;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-align: center;
            line-height: 60px;
            font-size: 1.8rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 100;
            transition: transform 0.3s;
        }
        
        .floating-whatsapp:hover {
            transform: scale(1.1);
        }

        /* التجاوبية */
        @media (max-width: 768px) {
            nav {
                flex-direction: column;
                padding: 1rem;
            }
            
            .nav-links {
                margin: 1rem 0;
                text-align: center;
            }
            
            .hero {
                flex-direction: column-reverse;
                text-align: center;
                padding: 3rem 5%;
            }
            
            .hero-content {
                padding-right: 0;
                margin-top: 2rem;
            }
            
            .hero-content h2 {
                font-size: 2rem;
            }
            
            .hero-buttons {
                justify-content: center;
            }
        }