<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منتجاتنا - مودا ماروك</title>
    <meta name="description" content="تصفح جميع منتجات الأزياء المغربية من مودا ماروك. جلابيات، قفاطين، عبايات وتصاميم فاخرة بجودة عالية وأسعار تنافسية.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
/* هذا هو ملف الستايل المحدث مع تحسينات احترافية لعرض أفضل على الهاتف */
/* أنماط عامة */
body {
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    line-height: 1.6;
    background-color: #f8f9fa;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}
.btn {
    display: inline-block;
    background-color: #d4af37;
    color: white;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}
.btn:hover {
    background-color: #a68b2a;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
.whatsapp-btn, .btn.whatsapp-btn {
    background-color: #25D366;
    color: white;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    border: none;
    transition: all 0.3s ease;
}
.whatsapp-btn i {
    margin-left: 8px;
}
.whatsapp-btn:hover {
    background-color: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
/* الشريط العلوي */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}
.logo h1 {
    margin: 0;
    color: #d4af37;
    font-weight: 700;
    font-size: 1.8rem;
}
.logo p {
    margin: 0;
    font-size: 0.8rem;
    color: #6c757d;
}
.nav-links {
    display: flex;
    align-items: center;
}
.nav-links a {
    margin: 0 15px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
    position: relative;
}
.nav-links a:hover {
    color: #d4af37;
}
.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    right: 0;
    background-color: #d4af37;
    transition: width 0.3s;
}
.nav-links a:hover::after {
    width: 100%;
    right: auto;
    left: 0;
}
/* قسم الفلترة */
.filter-section {
    padding: 2rem 0;
    background-color: #fff;
    border-bottom: 1px solid #eee;
}
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}
.filter-group {
    display: flex;
    flex-direction: column;
}
.filter-group label {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #555;
}
.filter-group select, .filter-group input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
}
.filter-btn {
    align-self: flex-end;
    padding: 10px 20px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 700;
    transition: background-color 0.3s;
}
.filter-btn:hover {
    background-color: #218838;
}
.reset-btn {
    align-self: flex-end;
    padding: 10px 20px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 700;
    transition: background-color 0.3s;
    margin-right: 10px;
}
.reset-btn:hover {
    background-color: #5a6268;
}
/* المنتجات */
.section-title {
    text-align: center;
    margin: 3rem 0 2rem;
    font-size: 2.5rem;
    color: #d4af37;
    font-weight: 700;
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}
.section-title::after {
    content: '';
    position: absolute;
    width: 50%;
    height: 3px;
    bottom: -10px;
    left: 25%;
    background-color: #d4af37;
}
.products {
    padding: 2rem 0 5rem;
    background-color: #fff;
}
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}
.product-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid #eee;
}
.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}
.product-card img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    transition: transform 0.5s ease;
}
.product-card:hover img {
    transform: scale(1.05);
}
.product-card-body {
    padding: 1.5rem;
}
.product-card h3 {
    margin: 0 0 0.5rem;
    color: #343a40;
    font-size: 1.3rem;
}
.product-card p {
    color: #6c757d;
    min-height: 60px;
}
.product-card .price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #d4af37;
    margin: 1rem 0;
}
/* الفوتر */
footer {
    background: #212529;
    color: #fff;
    padding: 3rem 0 1rem;
}
.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}
.footer-section h3 {
    color: #d4af37;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    position: relative;
    display: inline-block;
}
.footer-section h3::after {
    content: '';
    position: absolute;
    width: 50%;
    height: 2px;
    bottom: -8px;
    left: 0;
    background-color: #d4af37;
}
.footer-section p, .footer-section ul li {
    color: #adb5bd;
}
.footer-section ul {
    list-style: none;
    padding: 0;
}
.footer-section ul li {
    margin-bottom: 0.8rem;
    transition: transform 0.3s ease;
}
.footer-section ul li:hover {
    transform: translateX(-5px);
}
.footer-section ul li a {
    color: #adb5bd;
    text-decoration: none;
    transition: all 0.3s ease;
}
.footer-section ul li a:hover {
    color: #d4af37;
}
.social-icons {
    display: flex;
    gap: 15px;
}
.social-icons a {
    color: #adb5bd;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.social-icons a:hover {
    color: #d4af37;
    background-color: rgba(212, 175, 55, 0.1);
    transform: translateY(-3px);
}
.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #444;
    color: #adb5bd;
    font-size: 0.9rem;
}
/* زر الواتساب العائم */
.floating-whatsapp {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: #25D366;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    text-align: center;
    line-height: 60px;
    font-size: 1.8rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    z-index: 1000;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}
.floating-whatsapp:hover {
    transform: scale(1.1) translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    animation: none;
}
/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(37, 211, 102, 0); }
    100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
}
/* التجاوبية (Mobile-first Approach) */
/*----------------------------------*/
/*----------------------------------*/
/* الأنماط الخاصة بالشاشات الصغيرة (أقل من 768 بكسل) */
@media (max-width: 768px) {
    /* الشريط العلوي */
    nav {
        flex-direction: column;
        padding: 1rem;
    }
    .nav-links {
        margin: 1.5rem 0;
        text-align: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    .nav-links a {
        margin: 0.5rem 1rem;
    }
    /* قسم الفلترة */
    .filter-container {
        flex-direction: column;
        align-items: stretch;
    }
    .filter-group {
        width: 100%;
    }
    .filter-btn, .reset-btn {
        align-self: stretch;
        margin-right: 0;
        margin-top: 0.5rem;
    }
    .section-title {
        font-size: 2rem;
        left: auto;
        transform: none;
    }
    .product-card img {
        height: 300px;
    }
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
/* الأنماط الخاصة بالشاشات الصغيرة جدًا (أقل من 576 بكسل) */
@media (max-width: 576px) {
    .section-title {
        font-size: 1.8rem;
    }
    .floating-whatsapp {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 1.5rem;
        bottom: 20px;
        right: 20px;
    }
}
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1>مودا ماروك</h1>
                <p>أزياء مغربية عصرية</p>
            </div>
            <div class="nav-links">
                <a href="index.html">الرئيسية</a>
                <a href="products.html" style="color: #d4af37; font-weight: bold;">منتجاتنا</a>
                <a href="index.html#features">مميزاتنا</a>
                <a href="index.html#testimonials">آراء العملاء</a>
                <a href="index.html#contact">اتصل بنا</a>
            </div>
            <a href="https://wa.me/212612345678" target="_blank" class="whatsapp-btn">
                <i class="fab fa-whatsapp"></i> واتساب
            </a>
        </nav>
    </header>

    <section class="filter-section">
        <div class="container">
            <div class="filter-container">
                <div class="filter-group">
                    <label for="category-filter">التصنيف:</label>
                    <select id="category-filter">
                        <option value="all">الكل</option>
                        <option value="جلابة">جلابة</option>
                        <option value="قفطان">قفطان</option>
                        <option value="عباية">عباية</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="price-filter">السعر الأقصى:</label>
                    <input type="number" id="price-filter" placeholder="أدخل السعر" min="0">
                </div>
                <button class="filter-btn" id="apply-filter">تطبيق الفلتر</button>
                <button class="reset-btn" id="reset-filter">إعادة تعيين</button>
            </div>
        </div>
    </section>

    <section class="products">
        <div class="container">
            <h2 class="section-title">جميع منتجاتنا</h2>
            <div class="product-grid" id="product-grid">
                <!-- سيتم إضافة المنتجات عبر JavaScript -->
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>مودا ماروك</h3>
                    <p>وجهتك الأولى للأزياء المغربية الفاخرة والعصرية. نقدم أجود المنتجات بأسعار تنافسية مع خدمة عملاء مميزة.</p>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html" style="color: #d4af37;">منتجاتنا</a></li>
                        <li><a href="index.html#features">مميزاتنا</a></li>
                        <li><a href="index.html#testimonials">آراء العملاء</a></li>
                        <li><a href="index.html#contact">اتصل بنا</a></li>
                        <li><a href="#">سياسة الإرجاع</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>ساعات العمل</h3>
                    <ul>
                        <li>السبت - الخميس: 9 ص - 8 م</li>
                        <li>الجمعة: 11 ص - 6 م</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>تابعنا</h3>
                    <div class="social-icons">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="TikTok"><i class="fab fa-tiktok"></i></a>
                    </div>
                    <p style="margin-top: 1.5rem;">اشترك في نشرتنا البريدية</p>
                    <form id="newsletter-form">
                        <input type="email" placeholder="بريدك الإلكتروني" style="padding: 8px; border-radius: 5px; border: 1px solid #444; background: transparent; color: #fff; width: 100%; margin-bottom: 10px;" required>
                        <button type="submit" class="btn" style="width: 100%; padding: 8px;">اشترك</button>
                    </form>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <p>&copy; 2024 مودا ماروك. جميع الحقوق محفوظة.</p>
            </div>
        </div>
        <a href="https://wa.me/212612345678" class="floating-whatsapp" target="_blank" aria-label="Contact us on WhatsApp">
            <i class="fab fa-whatsapp"></i>
        </a>
    </footer>

    <script>
        // بيانات المنتجات (يمكن استبدالها بطلب AJAX في المستقبل)
        const products = [
            {
                id: 1,
                name: "جلابة مغربية فاخرة",
                description: "جلابة تقليدية مصنوعة يدويًا من الحرير الطبيعي بتطريزات ذهبية فاخرة، تناسب المناسبات الخاصة والأعياد.",
                price: 499,
                image: "https://i.pinimg.com/564x/f8/08/1b/f8081bdeee29035f15f14725d9bc1089.jpg",
                category: "جلابة",
                whatsappText: "أريد%20شراء%20الجلابة%20المغربية%20الفاخرة.%20(الرجاء%20تحديد%20المقاس%20واللون%20المطلوب)"
            },
            {
                id: 2,
                name: "قفطان أنيق للمناسبات",
                description: "قفطان بتطريزات عصرية تناسب الحفلات والسهرات، مصنوع من أجود أنواع القماش مع زخارف يدوية فاخرة.",
                price: 799,
                image: "https://lh3.googleusercontent.com/pw/AP1GczNlLkE02VvVY3H55znMOvWKD2Q2vUUM_bomwGwN7IIw2Csy9fBv4cky48_xeeHz1dYJN7h_f5TvFqWEaqHX3HGiH22U_tcvNtxO3pakCYiLuQ3tXYy_FjLbW26TX0Ka_69dhpgKDCpZIAkQmhqxpcc=w500-h500-s-no",
                category: "قفطان",
                whatsappText: "أريد%20شراء%20القفطان%20الأنيق.%20(الرجاء%20تحديد%20المقاس%20واللون%20المطلوب)"
            },
            {
                id: 3,
                name: "عباية يومية بسيطة",
                description: "عباية مريحة وعملية للاستخدام اليومي، مصنوعة من قماش خفيف الوزن يسمح بالحركة بسهولة مع الحفاظ على الأناقة.",
                price: 299,
                image: "https://images.unsplash.com/photo-1736342182213-6c037467cb38?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YWJheWF8ZW58MHx8MHx8fDA%3D",
                category: "عباية",
                whatsappText: "أريد%20شراء%20العباية%20اليومية%20البسيطة.%20(الرجاء%20تحديد%20المقاس%20واللون%20المطلوب)"
            },
            {
                id: 4,
                name: "جلابة صيفية خفيفة",
                description: "جلابة مصنوعة من قماش الكتان الخفيف والمنفس، مثالية للاستخدام الصيفي مع تصميم أنيق ومبسط.",
                price: 349,
                image: "https://images.unsplash.com/photo-1521334884684-d80222895326?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGphbGFifGVufDB8fDB8fHww",
                category: "جلابة",
                whatsappText: "أريد%20شراء%20الجلابة%20الصيفية%20الخفيفة.%20(الرجاء%20تحديد%20المقاس%20واللون%20المطلوب)"
            },
            {
                id: 5,
                name: "قفطان حريري فاخر",
                description: "قفطان مصنوع من أقمشة الحرير الفاخرة مع تطريزات مزركشة يدوية، يناسب الأعراس والمناسبات الكبرى.",
                price: 1299,
                image: "https://images.unsplash.com/photo-1534030347209-467a5b0ad3e6?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8c2FyYWZhbml8ZW58MHx8MHx8fDA%3D",
                category: "قفطان",
                whatsappText: "أريد%20شراء%20القفطان%20الحريري%20الفاخر.%20(الرجاء%20تحديد%20المقاس%20واللون%20المطلوب)"
            },
            {
                id: 6,
                name: "عباية بأكمام واسعة",
                description: "عباية عصرية بأكمام واسعة وتصميم أنيق، مصنوعة من قماش مريح ومناسب للاستخدام اليومي أو في المناسبات.",
                price: 399,
                image: "https://images.unsplash.com/photo-1591369822091-01ab6c7c7f3a?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8YWJheWF8ZW58MHx8MHx8fDA%3D",
                category: "عباية",
                whatsappText: "أريد%20شراء%20العباية%20بأكمام%20واسعة.%20(الرجاء%20تحديد%20المقاس%20واللون%20المطلوب)"
            }
        ];

        // دالة لعرض المنتجات
        function displayProducts(productsToShow) {
            const productGrid = document.getElementById('product-grid');
            productGrid.innerHTML = '';

            if (productsToShow.length === 0) {
                productGrid.innerHTML = '<p style="grid-column: 1 / -1; text-align: center; font-size: 1.2rem; color: #6c757d;">لا توجد منتجات تطابق الفلتر المحدد.</p>';
                return;
            }

            productsToShow.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.innerHTML = `
                    <img src="${product.image}" alt="${product.name}">
                    <div class="product-card-body">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="price">${product.price} درهم</div>
                        <a href="https://wa.me/212612345678?text=${product.whatsappText}" class="btn whatsapp-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i> اطلب الآن
                        </a>
                    </div>
                `;
                productGrid.appendChild(productCard);
            });
        }

        // دالة لتطبيق الفلتر
        function applyFilter() {
            const categoryFilter = document.getElementById('category-filter').value;
            const priceFilter = document.getElementById('price-filter').value;
            let filteredProducts = products;

            if (categoryFilter !== 'all') {
                filteredProducts = filteredProducts.filter(product => product.category === categoryFilter);
            }

            if (priceFilter && !isNaN(priceFilter)) {
                filteredProducts = filteredProducts.filter(product => product.price <= parseInt(priceFilter));
            }

            displayProducts(filteredProducts);
        }

        // دالة لإعادة تعيين الفلتر
        function resetFilter() {
            document.getElementById('category-filter').value = 'all';
            document.getElementById('price-filter').value = '';
            displayProducts(products);
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // عرض جميع المنتجات عند تحميل الصفحة
            displayProducts(products);

            // مستمع زر تطبيق الفلتر
            document.getElementById('apply-filter').addEventListener('click', applyFilter);

            // مستمع زر إعادة التعيين
            document.getElementById('reset-filter').addEventListener('click', resetFilter);

            // مستمع نموذج الاشتراك في النشرة البريدية
            document.getElementById('newsletter-form').addEventListener('submit', function(e) {
                e.preventDefault();
                const emailInput = this.querySelector('input[type="email"]');
                const email = emailInput.value.trim();

                if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    alert('شكرًا لاشتراكك في نشرتنا البريدية!');
                    emailInput.value = '';
                } else {
                    alert('الرجاء إدخال بريد إلكتروني صحيح.');
                }
            });
        });
    </script>
</body>
</html>