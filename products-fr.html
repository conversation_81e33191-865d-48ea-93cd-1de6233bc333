<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nos Produits - Moda Maroc</title>
    <meta name="description" content="Parcourez tous les produits de mode marocaine de Moda Maroc. Djellabas, caftans, abayas et designs de luxe de haute qualité à prix compétitifs.">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
/* Styles généraux */
body {
    font-family: 'Roboto', 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    line-height: 1.6;
    background-color: #f8f9fa;
    direction: ltr;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}
.btn {
    display: inline-block;
    background-color: #d4af37;
    color: white;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}
.btn:hover {
    background-color: #a68b2a;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
.whatsapp-btn, .btn.whatsapp-btn {
    background-color: #25D366;
    color: white;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    border: none;
    transition: all 0.3s ease;
}
.whatsapp-btn i {
    margin-right: 8px;
}
.whatsapp-btn:hover {
    background-color: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
}

/* Barre de navigation */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}
.logo h1 {
    margin: 0;
    color: #d4af37;
    font-weight: 700;
    font-size: 1.8rem;
}
.logo p {
    margin: 0;
    font-size: 0.8rem;
    color: #6c757d;
}
.nav-links {
    display: flex;
    align-items: center;
    gap: 20px;
}
.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
    padding: 5px 10px;
    border-radius: 3px;
}
.nav-links a:hover {
    color: #d4af37;
    background-color: #f8f9fa;
}

/* Sélecteur de langue */
.language-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}
.language-selector a {
    padding: 5px 10px;
    border-radius: 3px;
    text-decoration: none;
    color: #333;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}
.language-selector a:hover {
    background-color: #f0f0f0;
}
.language-selector a.active {
    background-color: #d4af37;
    color: white;
}

/* Section de filtrage */
.filter-section {
    background-color: #f8f9fa;
    padding: 2rem 0;
    border-bottom: 1px solid #dee2e6;
}
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: center;
}
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.filter-group label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}
.filter-group select,
.filter-group input {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 150px;
}
.filter-btn, .reset-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}
.filter-btn {
    background-color: #d4af37;
    color: white;
}
.filter-btn:hover {
    background-color: #a68b2a;
    transform: translateY(-1px);
}
.reset-btn {
    background-color: #6c757d;
    color: white;
}
.reset-btn:hover {
    background-color: #545b62;
    transform: translateY(-1px);
}

/* Produits */
.section-title {
    text-align: center;
    margin: 3rem 0 2rem;
    font-size: 2.5rem;
    color: #d4af37;
    font-weight: 700;
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}
.section-title::after {
    content: '';
    position: absolute;
    width: 50%;
    height: 3px;
    bottom: -10px;
    left: 25%;
    background-color: #d4af37;
}
.products {
    padding: 2rem 0 5rem;
    background-color: #fff;
}
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}
.product-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid #eee;
}
.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 25px rgba(0,0,0,0.15);
}
.product-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
}
.product-card:hover img {
    transform: scale(1.05);
}
.product-card-body {
    padding: 1.5rem;
}
.product-card h3 {
    margin: 0 0 0.5rem;
    color: #343a40;
    font-size: 1.3rem;
}
.product-card p {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}
.product-card .price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #d4af37;
    margin: 1rem 0;
}

/* Newsletter */
.newsletter {
    background: linear-gradient(135deg, #d4af37 0%, #a68b2a 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}
.newsletter h3 {
    margin-bottom: 1rem;
    font-size: 1.8rem;
}
.newsletter p {
    margin-bottom: 2rem;
    opacity: 0.9;
}
.newsletter-form {
    display: flex;
    justify-content: center;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}
.newsletter-form input {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
}
.newsletter-form button {
    padding: 0.75rem 1.5rem;
    background-color: #fff;
    color: #d4af37;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}
.newsletter-form button:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
}

/* Pied de page */
footer {
    background-color: #343a40;
    color: #fff;
    padding: 3rem 0 0;
}
.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    padding: 0 5%;
}
.footer-section h3 {
    color: #d4af37;
    margin-bottom: 1rem;
}
.footer-section ul {
    list-style: none;
    padding: 0;
}
.footer-section ul li {
    margin-bottom: 0.5rem;
}
.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}
.footer-section ul li a:hover {
    color: #d4af37;
}
.footer-bottom {
    background-color: #212529;
    padding: 1rem 0;
    margin-top: 2rem;
    text-align: center;
}
.footer-bottom p {
    margin: 0;
    color: #ccc;
}

/* WhatsApp flottant */
.floating-whatsapp {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #25D366;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: transform 0.3s;
    z-index: 1000;
}
.floating-whatsapp:hover {
    transform: scale(1.1);
}

/* Responsive */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }
    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }
    .filter-container {
        flex-direction: column;
        align-items: stretch;
    }
    .filter-group {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    .filter-group label {
        margin-bottom: 0;
        min-width: 100px;
    }
    .newsletter-form {
        flex-direction: column;
        max-width: 300px;
    }
    .floating-whatsapp {
        bottom: 15px;
        right: 15px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}
@media (max-width: 480px) {
    .section-title {
        font-size: 2rem;
    }
    .product-grid {
        grid-template-columns: 1fr;
        padding: 0 1rem;
    }
    .footer-content {
        grid-template-columns: 1fr;
        padding: 0 2rem;
    }
}
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1>Moda Maroc</h1>
                <p>Mode marocaine contemporaine</p>
            </div>
            <div class="nav-links">
                <a href="index-fr.html">Accueil</a>
                <a href="products-fr.html" style="color: #d4af37; font-weight: bold;">Nos Produits</a>
                <a href="index-fr.html#features">Nos Avantages</a>
                <a href="index-fr.html#testimonials">Avis Clients</a>
                <a href="index-fr.html#contact">Contact</a>
            </div>
            <div class="language-selector">
                <a href="products.html">العربية</a>
                <a href="products-fr.html" class="active">Français</a>
            </div>
            <a href="https://wa.me/212612345678" target="_blank" class="whatsapp-btn">
                <i class="fab fa-whatsapp"></i> WhatsApp
            </a>
        </nav>
    </header>

    <section class="filter-section">
        <div class="container">
            <div class="filter-container">
                <div class="filter-group">
                    <label for="category-filter">Catégorie :</label>
                    <select id="category-filter">
                        <option value="all">Toutes</option>
                        <option value="djellaba">Djellaba</option>
                        <option value="caftan">Caftan</option>
                        <option value="abaya">Abaya</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="price-filter">Prix maximum :</label>
                    <input type="number" id="price-filter" placeholder="Entrez le prix" min="0">
                </div>
                <button class="filter-btn" id="apply-filter">Appliquer le Filtre</button>
                <button class="reset-btn" id="reset-filter">Réinitialiser</button>
            </div>
        </div>
    </section>

    <section class="products">
        <div class="container">
            <h2 class="section-title">Tous Nos Produits</h2>
            <div class="product-grid" id="product-grid">
                <!-- Les produits seront ajoutés via JavaScript -->
            </div>
        </div>
    </section>

    <section class="newsletter">
        <div class="container">
            <h3>Restez Informé de Nos Nouveautés</h3>
            <p>Inscrivez-vous à notre newsletter pour recevoir les dernières offres et nouveaux produits</p>
            <form class="newsletter-form" id="newsletter-form">
                <input type="email" placeholder="Votre adresse email" required>
                <button type="submit">S'inscrire</button>
            </form>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Moda Maroc</h3>
                    <p>Votre destination première pour la mode marocaine de luxe et contemporaine. Nous offrons les meilleurs produits à des prix compétitifs avec un service client exceptionnel.</p>
                </div>
                <div class="footer-section">
                    <h3>Liens Rapides</h3>
                    <ul>
                        <li><a href="index-fr.html">Accueil</a></li>
                        <li><a href="products-fr.html" style="color: #d4af37;">Nos Produits</a></li>
                        <li><a href="index-fr.html#features">Nos Avantages</a></li>
                        <li><a href="index-fr.html#testimonials">Avis Clients</a></li>
                        <li><a href="index-fr.html#contact">Contact</a></li>
                        <li><a href="#">Politique de Retour</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Heures d'Ouverture</h3>
                    <ul>
                        <li>Samedi - Jeudi : 9h - 20h</li>
                        <li>Vendredi : 11h - 18h</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Suivez-Nous</h3>
                    <ul>
                        <li><a href="#"><i class="fab fa-facebook"></i> Facebook</a></li>
                        <li><a href="#"><i class="fab fa-instagram"></i> Instagram</a></li>
                        <li><a href="#"><i class="fab fa-twitter"></i> Twitter</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <div class="container">
                <p>&copy; 2024 Moda Maroc. Tous droits réservés.</p>
            </div>
        </div>
        <a href="https://wa.me/212612345678" class="floating-whatsapp" target="_blank" aria-label="Contactez-nous sur WhatsApp">
            <i class="fab fa-whatsapp"></i>
        </a>
    </footer>

    <script>
        // Données des produits (peut être remplacé par une requête AJAX à l'avenir)
        const products = [
            {
                id: 1,
                name: "Djellaba Marocaine de Luxe",
                description: "Djellaba traditionnelle faite à la main en soie naturelle avec des broderies dorées luxueuses, parfaite pour les occasions spéciales et les fêtes.",
                price: 499,
                image: "https://i.pinimg.com/564x/f8/08/1b/f8081bdeee29035f15f14725d9bc1089.jpg",
                category: "djellaba",
                whatsappText: "Je%20souhaite%20acheter%20la%20Djellaba%20Marocaine%20de%20Luxe.%20(Veuillez%20préciser%20la%20taille%20et%20la%20couleur%20souhaitées)"
            },
            {
                id: 2,
                name: "Caftan Élégant",
                description: "Caftan moderne avec des détails brodés délicats, fabriqué à partir de tissus de haute qualité pour un look sophistiqué et confortable.",
                price: 599,
                image: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8a2FmdGFufGVufDB8fDB8fHww",
                category: "caftan",
                whatsappText: "Je%20souhaite%20acheter%20le%20Caftan%20Élégant.%20(Veuillez%20préciser%20la%20taille%20et%20la%20couleur%20souhaitées)"
            },
            {
                id: 3,
                name: "Caftan de Soirée",
                description: "Caftan sophistiqué avec des perles et des paillettes, idéal pour les soirées et les événements formels. Design unique et finitions de luxe.",
                price: 799,
                image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8a2FmdGFufGVufDB8fDB8fHww",
                category: "caftan",
                whatsappText: "Je%20souhaite%20acheter%20le%20Caftan%20de%20Soirée.%20(Veuillez%20préciser%20la%20taille%20et%20la%20couleur%20souhaitées)"
            },
            {
                id: 4,
                name: "Djellaba Moderne",
                description: "Djellaba au design contemporain qui allie tradition et modernité, fabriquée avec des matériaux de qualité supérieure pour un confort optimal.",
                price: 349,
                image: "https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8a2FmdGFufGVufDB8fDB8fHww",
                category: "djellaba",
                whatsappText: "Je%20souhaite%20acheter%20la%20Djellaba%20Moderne.%20(Veuillez%20préciser%20la%20taille%20et%20la%20couleur%20souhaitées)"
            },
            {
                id: 5,
                name: "Abaya Quotidienne Simple",
                description: "Abaya confortable et pratique pour un usage quotidien, fabriquée en tissu léger qui permet une liberté de mouvement tout en conservant l'élégance.",
                price: 299,
                image: "https://images.unsplash.com/photo-1736342182213-6c037467cb38?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YWJheWF8ZW58MHx8MHx8fDA%3D",
                category: "abaya",
                whatsappText: "Je%20souhaite%20acheter%20l'Abaya%20Quotidienne%20Simple.%20(Veuillez%20préciser%20la%20taille%20et%20la%20couleur%20souhaitées)"
            },
            {
                id: 6,
                name: "Abaya à Manches Larges",
                description: "Abaya contemporaine à manches larges et design élégant, fabriquée en tissu confortable et adaptée à un usage quotidien ou pour les occasions.",
                price: 399,
                image: "https://images.unsplash.com/photo-1591369822091-01ab6c7c7f3a?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8YWJheWF8ZW58MHx8MHx8fDA%3D",
                category: "abaya",
                whatsappText: "Je%20souhaite%20acheter%20l'Abaya%20à%20Manches%20Larges.%20(Veuillez%20préciser%20la%20taille%20et%20la%20couleur%20souhaitées)"
            }
        ];

        // Fonction pour afficher les produits
        function displayProducts(productsToShow = products) {
            const productGrid = document.getElementById('product-grid');
            productGrid.innerHTML = '';

            productsToShow.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.innerHTML = `
                    <img src="${product.image}" alt="${product.name}">
                    <div class="product-card-body">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <div class="price">${product.price} DH</div>
                        <a href="https://wa.me/212612345678?text=${product.whatsappText}" class="btn whatsapp-btn" target="_blank">
                            <i class="fab fa-whatsapp"></i> Commander Maintenant
                        </a>
                    </div>
                `;
                productGrid.appendChild(productCard);
            });
        }

        // Fonction de filtrage
        function filterProducts() {
            const categoryFilter = document.getElementById('category-filter').value;
            const priceFilter = parseFloat(document.getElementById('price-filter').value) || Infinity;

            let filteredProducts = products;

            if (categoryFilter !== 'all') {
                filteredProducts = filteredProducts.filter(product => product.category === categoryFilter);
            }

            filteredProducts = filteredProducts.filter(product => product.price <= priceFilter);

            displayProducts(filteredProducts);
        }

        // Fonction de réinitialisation
        function resetFilters() {
            document.getElementById('category-filter').value = 'all';
            document.getElementById('price-filter').value = '';
            displayProducts(products);
        }

        // Événements
        document.addEventListener('DOMContentLoaded', function() {
            // Affichage initial des produits
            displayProducts();

            // Écouteurs d'événements pour les filtres
            document.getElementById('apply-filter').addEventListener('click', filterProducts);
            document.getElementById('reset-filter').addEventListener('click', resetFilters);

            // Écouteur d'événement pour le formulaire de newsletter
            document.getElementById('newsletter-form').addEventListener('submit', function(e) {
                e.preventDefault();
                const emailInput = this.querySelector('input[type="email"]');
                const email = emailInput.value.trim();

                if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    alert('Merci de vous être inscrit à notre newsletter !');
                    emailInput.value = '';
                } else {
                    alert('Veuillez entrer une adresse email valide.');
                }
            });
        });
    </script>
</body>
</html>
